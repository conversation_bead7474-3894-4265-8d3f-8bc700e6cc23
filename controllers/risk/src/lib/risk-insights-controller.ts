import { riskManagementControllerGetDashboardOptions } from '@globals/api-sdk/queries';
import type { DashboardResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class RiskInsightsController {
    constructor() {
        makeAutoObservable(this);
    }

    riskInsightsQuery = new ObservedQuery(
        riskManagementControllerGetDashboardOptions,
    );

    get isLoading(): boolean {
        return this.riskInsightsQuery.isLoading;
    }

    get riskInsights(): DashboardResponseDto | null {
        return this.riskInsightsQuery.data;
    }

    initialize = () => {
        when(
            () => sharedWorkspacesController.isReady,
            () => {
                this.load();
            },
        );
    };

    load = () => {
        this.riskInsightsQuery.load({ query: { isScored: true } });
    };
}

export const sharedRiskInsightsController = new RiskInsightsController();
