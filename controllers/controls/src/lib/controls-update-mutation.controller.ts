import { uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerEditControlMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedControlCustomFieldsController } from './control-custom-fields.controller';
import { sharedControlCustomFieldsMutationController } from './control-custom-fields-mutation.controller';
import { sharedControlDetailsController } from './control-details.controller';

export class ControlsUpdateMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    updateControlMutation = new ObservedMutation(
        grcControllerEditControlMutation,
    );

    get isControlUpdateLoading(): boolean {
        return this.updateControlMutation.isPending;
    }

    get hasControlUpdateError(): boolean {
        return this.updateControlMutation.hasError;
    }

    updateControl = (
        controlId: number,
        requestBody: NonNullable<
            NonNullable<
                Parameters<typeof grcControllerEditControlMutation>[0]
            >['body']
        >,
        customFields?: Record<string, unknown> | null,
    ): void => {
        const { currentWorkspaceId } = sharedWorkspacesController;

        this.updateControlMutation
            .mutateAsync({
                path: { controlId, xProductId: currentWorkspaceId as number },
                body: requestBody,
            })
            .then(() => {
                if (customFields) {
                    sharedControlCustomFieldsMutationController.submitCustomFields(
                        controlId,
                        customFields,
                    );
                }

                when(
                    () =>
                        !sharedControlCustomFieldsMutationController.isSubmitting,
                    () => {
                        if (
                            sharedControlCustomFieldsMutationController.hasError
                        ) {
                            snackbarController.addSnackbar({
                                id: `control-update-error-${uniqueId()}`,
                                props: {
                                    title: t`Failed to update control`,
                                    description: t`An error occurred while updating the control. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        }

                        snackbarController.addSnackbar({
                            id: `control-update-success-${uniqueId()}`,
                            props: {
                                title: t`Control updated successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        sharedControlDetailsController.controlDetailsQuery.invalidate();
                        sharedControlCustomFieldsController.controlCustomFieldsQuery.invalidate();
                    },
                );
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `control-update-error-${uniqueId()}`,
                    props: {
                        title: t`Failed to update control`,
                        description: t`An error occurred while updating the control. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedControlsUpdateMutationController =
    new ControlsUpdateMutationController();
