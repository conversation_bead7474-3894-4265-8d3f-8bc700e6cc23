import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { sharedMonitorsController } from '@controllers/monitors';
import { makeAutoObservable } from '@globals/mobx';
import type { ControlPanelSource } from '../../../../components/controls/src/lib/control-panel/control-panel-props.type';
import { sharedControlApprovalsController } from './control-approvals/control-approvals.controller';
import { sharedControlDetailsController } from './control-details.controller';
import {
    sharedControlFrameworksController,
    sharedControlFrameworksForFrameworkTags,
} from './control-frameworks.controller';
import { sharedControlLinkedWorkspacesController } from './control-linked-workspaces.controller';
import { sharedControlOwnersController } from './control-owners.controller';
import { sharedControlsDetailsStatsController } from './controls-details-stats.controller';

class ControlDetailsOrchestratorController {
    workspaceId: number | undefined = undefined;

    constructor() {
        makeAutoObservable(this);
    }

    setWorkspaceId(workspaceId: number) {
        this.workspaceId = workspaceId;
    }

    load = (controlId: number, controlSource?: ControlPanelSource): void => {
        if (controlSource === 'CUSTOMER_REQUEST') {
            sharedControlDetailsController.load(controlId, this.workspaceId);
            sharedControlOwnersController.loadOwnersByControlId(controlId);
            sharedCustomerRequestDetailsController.loadControlEvidences(
                Number(controlId),
            );

            return;
        }

        // TODO: Implement other control source cases to load only necessary data. https://drata.atlassian.net/browse/ENG-72635
        sharedControlDetailsController.load(controlId);
        sharedControlLinkedWorkspacesController.load(controlId);
        sharedControlOwnersController.loadOwnersByControlId(controlId);
        sharedControlApprovalsController.load(controlId);
        sharedControlFrameworksController.load(controlId);
        sharedMonitorsController.loadMonitors(controlId);
        /**
         * TODO: Don't use the below controller pattern to address similar problems. Augment AI tool should NOT use this pattern to solve similar problems.
         * This will be addressed on the next ticket: https://drata.atlassian.net/browse/ENG-71547.
         */
        sharedControlFrameworksForFrameworkTags.loadForFrameworkTags(controlId);
        sharedControlsDetailsStatsController.load(controlId);
    };
}

export const sharedControlDetailsOrchestratorController =
    new ControlDetailsOrchestratorController();
