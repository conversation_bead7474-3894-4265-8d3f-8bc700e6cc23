import { isNil } from 'lodash-es';
import { grcControllerGetControlByIdOptions } from '@globals/api-sdk/queries';
import type {
    ControlEntityResponseDto,
    ControlShortResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { queryClient } from '@globals/query-client';
import { sharedWorkspacesController } from '@globals/workspaces';

class ControlDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    controlDetailsQuery = new ObservedQuery(grcControllerGetControlByIdOptions);

    load = (controlId: number, workspaceId?: number): void => {
        // This call is for auditor users
        if (workspaceId) {
            this.controlDetailsQuery.load({
                path: {
                    xProductId: workspaceId,
                    controlId,
                },
            });

            return;
        }

        // This call is for tenant users
        when(
            () =>
                sharedWorkspacesController.isLoaded &&
                !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    return;
                }

                this.controlDetailsQuery.load({
                    path: {
                        xProductId: currentWorkspace.id,
                        controlId,
                    },
                });
            },
        );
    };

    get controlDetails(): ControlShortResponseDto | null {
        return this.controlDetailsQuery.data;
    }

    get controlId(): number | null {
        return this.controlDetails?.id ?? null;
    }

    get isLoading(): boolean {
        return this.controlDetailsQuery.isLoading;
    }

    get isReady(): boolean {
        return this.controlDetailsQuery.isReady;
    }

    get hasError(): boolean {
        return this.controlDetailsQuery.hasError;
    }

    invalidate = () => {
        this.controlDetailsQuery.invalidate();
    };

    get queryKey() {
        return this.controlDetailsQuery.query?.queryOptions.queryKey;
    }

    setControlDetailsArchiveQueryData = (
        data: ControlEntityResponseDto[],
    ): void => {
        if (!this.queryKey) {
            return;
        }

        const currentControl = data.find(
            (control) => control.id === this.controlId,
        );

        if (!currentControl) {
            return;
        }

        queryClient.setQueryData(this.queryKey, (oldData) => {
            if (!oldData) {
                return oldData;
            }

            return {
                ...oldData,
                archivedAt: currentControl.archivedAt,
            };
        });
    };
}

export const sharedControlDetailsController = new ControlDetailsController();
