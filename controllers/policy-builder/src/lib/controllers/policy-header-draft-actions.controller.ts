import { isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import {
    policiesControllerDeletePolicyVersionMutation,
    policiesControllerResetPolicyMutation,
    policyVersionControllerPutPolicyVersionVersionAndStatusMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import {
    closePolicyFinalizeModal,
    openPolicyFinalizeModal,
} from '../helpers/policy-finalize-modal.helper';
import { sharedPolicyBuilderController } from '../policy-builder.controller';
import { sharedPolicyHeaderSharedActionsController } from './policy-header-shared-actions.controller';

export class PolicyHeaderDraftActions {
    deleteDraftMutation = new ObservedMutation(
        policiesControllerDeletePolicyVersionMutation,
        {
            onSuccess: () => {
                this.handlePostDeleteNavigation();
            },
        },
    );

    resetPolicyMutation = new ObservedMutation(
        policiesControllerResetPolicyMutation,
        {
            onSuccess: () => {
                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
        },
    );

    finalizeDraftMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionVersionAndStatusMutation,
        {
            onSuccess: () => {
                sharedPolicyBuilderController.invalidatePolicyQueries();
                closePolicyFinalizeModal();

                snackbarController.addSnackbar({
                    id: 'finalize-draft-success',
                    hasTimeout: true,
                    props: {
                        title: t`Draft finalized successfully`,
                        description: t`The policy has been moved to approval status`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'finalize-draft-error',
                    props: {
                        title: t`Failed to finalize draft`,
                        description: t`An error occurred while finalizing the draft`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    constructor() {
        makeAutoObservable(this);
    }

    private get shouldShowAuthorPolicy(): boolean {
        return sharedPolicyBuilderModel.isUploadedPolicy;
    }

    private get shouldShowPdfPreview(): boolean {
        const { isAuthoredPolicy, hasHtmlContent } = sharedPolicyBuilderModel;

        return Boolean(isAuthoredPolicy && hasHtmlContent);
    }

    private get shouldShowUploadFile(): boolean {
        const { isUploadedPolicy, hasTemplate } = sharedPolicyBuilderModel;

        return isUploadedPolicy || hasTemplate;
    }

    get isDeletingDraft(): boolean {
        return this.deleteDraftMutation.isPending;
    }

    get hasDeleteDraftError(): boolean {
        return this.deleteDraftMutation.hasError;
    }

    get isResettingPolicy(): boolean {
        return this.resetPolicyMutation.isPending;
    }

    get hasResetPolicyError(): boolean {
        return this.resetPolicyMutation.hasError;
    }

    get isFinalizingDraft(): boolean {
        return this.finalizeDraftMutation.isPending;
    }

    getActions(): Action[] {
        const actions: Action[] = [];

        const dropdownActions = this.getDropdownActions();

        if (!isEmpty(dropdownActions)) {
            actions.push({
                id: 'draft-dropdown-actions',
                actionType: 'dropdown',
                typeProps: {
                    label: '',
                    level: 'tertiary',
                    colorScheme: 'primary',
                    startIconName: 'HorizontalMenu',
                    items: dropdownActions,
                },
            });

            if (this.shouldShowFinalizeDraftButton) {
                actions.push(this.finalizeDraftAction);
            }
        }

        return actions;
    }

    private get finalizeDraftAction(): Action {
        return {
            id: 'finalize-draft-button',
            actionType: 'button',
            typeProps: {
                label: t`Finalize draft`,
                level: 'primary',
                colorScheme: 'primary',
                onClick: this.handleFinalizeDraft,
                disabled: this.isFinalizingDraft,
            },
        };
    }

    private getDropdownActions(): SchemaDropdownItemData[] {
        const actions: SchemaDropdownItemData[] = [];

        if (this.shouldShowPdfPreview) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.pdfPreviewAction,
            );
        }

        if (this.shouldShowUploadFile) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.uploadFileAction,
            );
        }

        if (this.shouldShowAuthorPolicy) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.authorPolicyAction,
            );
        }

        if (this.shouldShowRestartWithTemplate) {
            actions.push({
                id: 'restart-with-template-action',
                type: 'item',
                label: t`Restart with template`,
                value: 'restart-with-template',
                onSelect: this.handleRestartWithTemplate,
                disabled: this.isResettingPolicy,
            });
        }

        if (this.shouldShowDeleteDraft) {
            actions.push({
                id: 'delete-draft-action',
                type: 'item',
                label: t`Delete draft`,
                value: 'delete-draft',
                onSelect: this.handleDeleteDraft,
                disabled: this.isDeletingDraft,
            });
        }

        return actions;
    }

    private get shouldShowFinalizeDraftButton(): boolean {
        return true;
    }

    private get shouldShowRestartWithTemplate(): boolean {
        const { hasTemplate } = sharedPolicyBuilderModel;

        return Boolean(hasTemplate);
    }

    private get shouldShowDeleteDraft(): boolean {
        if (sharedPolicyBuilderModel.isNewDrataTemplatePolicy) {
            return false;
        }

        return (
            sharedPolicyBuilderModel.isAuthoredPolicy ||
            sharedPolicyBuilderModel.isUploadedPolicy ||
            sharedPolicyBuilderModel.hasNotionOrConfluenceConnection
        );
    }

    handleFinalizeDraft = (): void => {
        openPolicyFinalizeModal();
    };

    handleFinalizeDraftFromModal = (data: {
        isMaterialChange: boolean;
        requiresApproval: boolean;
        requiresAcknowledgment: boolean;
        changesExplanation: string;
    }): void => {
        const { policyId, currentVersionId } = sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId) {
            snackbarController.addSnackbar({
                id: 'finalize-draft-no-ids',
                props: {
                    title: t`Unable to finalize draft`,
                    description: t`Policy or version ID not found`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        // Use the correct endpoint with all the form data
        this.finalizeDraftMutation.mutate({
            path: {
                policyId,
                policyVersionId: currentVersionId,
            },
            body: {
                isMaterialChange: data.isMaterialChange,
                requiresApproval: data.requiresApproval,
                requiresAcknowledgment: data.requiresAcknowledgment,
                explanationOfChanges: data.changesExplanation,
                shouldNotifyEmployees: false,
            },
        });
    };

    handleRestartWithTemplate = (): void => {
        openConfirmationModal({
            title: t`Restart from template?`,
            body: t`Restarting from the latest Drata template will override your current policy and restore the Drata policy template. The description, policy owner, renewal date, and SLA (if applicable) will not be changed. Are you sure you'd like to continue?`,
            confirmText: t`Continue`,
            cancelText: t`Cancel`,
            type: 'primary',
            size: 'sm',
            disableClickOutsideToClose: true,
            onConfirm: this.confirmRestartWithTemplate,
            onCancel: closeConfirmationModal,
            isLoading: () => this.isResettingPolicy,
        });
    };

    handleDeleteDraft = (): void => {
        openConfirmationModal({
            title: t`Delete draft`,
            body: t`Are you sure you want to delete this draft? This action cannot be undone.`,
            confirmText: t`Delete`,
            cancelText: t`Cancel`,
            type: 'danger',
            size: 'md',
            disableClickOutsideToClose: true,
            onConfirm: this.confirmDeleteDraft,
            onCancel: closeConfirmationModal,
            isLoading: () => this.isDeletingDraft,
        });
    };

    confirmDeleteDraft = (): void => {
        const { currentVersionId } = sharedPolicyBuilderController;

        if (!currentVersionId) {
            return;
        }

        this.deleteDraftMutation.mutate({
            path: { id: currentVersionId },
        });

        when(
            () => !this.isDeletingDraft,
            () => {
                if (this.hasDeleteDraftError) {
                    snackbarController.addSnackbar({
                        id: 'delete-draft-error',
                        props: {
                            title: t`Failed to delete draft`,
                            description: t`An error occurred while deleting the draft`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'delete-draft-success',
                    hasTimeout: true,
                    props: {
                        title: t`Draft deleted successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                closeConfirmationModal();
            },
        );
    };

    handlePostDeleteNavigation = (): void => {
        const { hasPublishedVersion, hasDraftVersion } =
            sharedPolicyBuilderModel;

        if (!hasPublishedVersion && !hasDraftVersion) {
            this.navigateToPoliciesList();
        } else {
            this.loadPublishedVersion();
        }
    };

    navigateToPoliciesList = (): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspaceId;

        sharedProgrammaticNavigationController.navigateTo(
            `/workspaces/${workspaceId}/governance/policies/active`,
        );
    };

    loadPublishedVersion = (): void => {
        const { publishedVersionId, policyId } = sharedPolicyBuilderModel;

        if (publishedVersionId) {
            sharedPolicyBuilderController.loadPolicyWithAllData(
                policyId,
                publishedVersionId,
            );
        }
    };

    confirmRestartWithTemplate = (): void => {
        const { policyId } = sharedPolicyBuilderModel;

        this.resetPolicyMutation.mutate({
            path: { id: policyId },
        });

        when(
            () => !this.isResettingPolicy,
            () => {
                if (this.hasResetPolicyError) {
                    snackbarController.addSnackbar({
                        id: 'restart-policy-error',
                        props: {
                            title: t`Failed to restart policy`,
                            description: t`An error occurred while restarting the policy. Please try again or contact support.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'restart-policy-success',
                    hasTimeout: true,
                    props: {
                        title: t`Policy restarted successfully`,
                        description: t`The policy has been reset to the latest template`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                closeConfirmationModal();
            },
        );
    };
}

export const sharedPolicyHeaderDraftActions = new PolicyHeaderDraftActions();
