import { useCallback } from 'react';
import { sharedRiskInsightsController } from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    getRiskTreatmentLabel,
    getTreatmentColor,
    getTreatmentIcon,
} from '@helpers/risk-treatment';
import { useNavigate } from '@remix-run/react';

const TREATMENT_TYPES = [
    'ACCEPT',
    'AVOID',
    'MITIGATE',
    'TRANSFER',
    'UNTREATED',
] as const;

export const TreatmentOverviewCard = observer((): React.JSX.Element => {
    const { riskInsights } = sharedRiskInsightsController;

    const treatmentOverview = riskInsights?.treatmentOverview ?? {};
    const untreatedValue = (treatmentOverview.UNTREATED as number) || 0;

    const { currentWorkspace } = sharedWorkspacesController;
    const navigate = useNavigate();

    const handleOnClick = useCallback(() => {
        if (!currentWorkspace) {
            return;
        }

        navigate(`/workspaces/${currentWorkspace.id}/risk/register/management`);
    }, [currentWorkspace, navigate]);

    return (
        <Card
            title={t`Treatment Overview`}
            tooltipText={t`Treatment overview`}
            data-testid="TreatmentOverviewCard"
            data-id="o96LYHxl"
            body={
                <Box
                    data-id="q6JvnF-e"
                    borderColor="neutralBorderFaded"
                    p="lg"
                    width="100%"
                >
                    <Grid
                        columns={{ initial: '1', sm: '2', md: '3', lg: '5' }}
                        gap="md"
                        align="stretch"
                    >
                        <StatBlock
                            isInteractive
                            title={getRiskTreatmentLabel('UNTREATED')}
                            statValue={untreatedValue}
                            statIcon={getTreatmentIcon(
                                'UNTREATED',
                                untreatedValue,
                            )}
                            statIconColor={getTreatmentColor(
                                'UNTREATED',
                                untreatedValue,
                            )}
                            onClick={handleOnClick}
                        />
                        {TREATMENT_TYPES.map((type) => (
                            <StatBlock
                                isInteractive
                                key={type}
                                title={getRiskTreatmentLabel(type)}
                                statValue={treatmentOverview[type] || 0}
                                data-id="aCfZhtEg"
                                onClick={handleOnClick}
                            />
                        ))}
                    </Grid>
                </Box>
            }
            actions={[
                {
                    actionType: 'button',
                    id: 'download-treatment-overview-button',
                    typeProps: {
                        isIconOnly: true,
                        label: 'Download',
                        startIconName: 'Download',
                        level: 'tertiary',
                    },
                },
            ]}
        />
    );
});
