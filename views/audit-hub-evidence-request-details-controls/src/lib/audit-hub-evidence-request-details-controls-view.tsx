import { AppDatatable } from '@components/app-datatable';
import { ControlPanel } from '@components/controls';
import { openLinkControlsModalWithWorkspace } from '@components/evidence-library';
import {
    sharedAuditHubController,
    sharedAuditHubControlsController,
} from '@controllers/audit-hub';
import { sharedControlDetailsOrchestratorController } from '@controllers/controls';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { panelController } from '@controllers/panel';
import type { CustomerRequestEvidenceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    getAuditHubDefaultPaginationOptions,
    getAuditHubTableSearchProps,
} from '@helpers/audit-hub-table';
import { sharedCustomerRequestControlsBulkActionsModel } from '@models/auditor-client-audit';
import { getEvidenceRequestDetailsControlsColumns } from './constants/evidence-controls-columns';

export const EvidenceRequestDetailsControlsView = observer((): JSX.Element => {
    const { downloadAllControls, updateRequestControls } =
        sharedCustomerRequestDetailsController;

    const {
        auditCustomerRequestControls,
        auditCustomerRequestControlsIsLoading,
        auditCustomerRequestControlsTotal,
        loadControlsPage,
    } = sharedAuditHubControlsController;

    const { auditByIdData } = sharedAuditHubController;
    const workspaceId = auditByIdData?.framework.productId;
    const { getRequestId: requestId, auditorFrameworkId: auditId } =
        sharedCustomerRequestDetailsController;

    const { bulkActions, handleRowSelection, isEvidencePackageDownloading } =
        sharedCustomerRequestControlsBulkActionsModel;

    return (
        <AppDatatable
            isRowSelectionEnabled
            getRowId={(row) => String(row.id)}
            isLoading={auditCustomerRequestControlsIsLoading}
            tableId="datatable-audit-hub-evidence-request-details-controls"
            data={auditCustomerRequestControls}
            total={auditCustomerRequestControlsTotal}
            data-testid="EvidenceRequestDetailsControlsView"
            data-id="U9j_XYCl"
            columns={getEvidenceRequestDetailsControlsColumns()}
            tableSearchProps={getAuditHubTableSearchProps()}
            defaultPaginationOptions={getAuditHubDefaultPaginationOptions()}
            bulkActionDropdownItems={bulkActions}
            defaultColumnOptions={{
                minSize: 5,
            }}
            tableActions={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        isIconOnly: false,
                        label: t`Download all`,
                        level: 'tertiary',
                        colorScheme: 'neutral',
                        isLoading: isEvidencePackageDownloading,
                        a11yLoadingLabel: t`Downloading evidence package`,
                        onClick: downloadAllControls,
                    },
                },
                {
                    actionType: 'button',
                    id: 'map-controls-button',
                    typeProps: {
                        label: t`Map controls`,
                        level: 'secondary',
                        onClick: () => {
                            const currentControlIds: number[] = [];

                            openLinkControlsModalWithWorkspace({
                                objectType: 'risk',
                                onConfirm: (selectedControls) => {
                                    const controlIds = selectedControls.map(
                                        (item) => item.controlData.id,
                                    );

                                    updateRequestControls(
                                        controlIds as number[],
                                    );
                                },
                                excludeControlIds: currentControlIds,
                                auditId: auditId || undefined,
                                productId: workspaceId,
                                excludeRequestId: requestId || undefined,
                            });
                        },
                    },
                },
            ]}
            emptyStateProps={{
                illustrationName: 'Warning',
                title: t`Controls`,
                description: t`No controls were found`,
            }}
            onFetchData={loadControlsPage}
            onRowSelection={handleRowSelection}
            onRowClick={({
                row,
            }: {
                row: CustomerRequestEvidenceResponseDto;
            }) => {
                action(() => {
                    sharedControlDetailsOrchestratorController.setWorkspaceId(
                        workspaceId as number,
                    );
                    sharedControlDetailsOrchestratorController.load(
                        Number(row.id),
                        'CUSTOMER_REQUEST',
                    );
                    panelController.openPanel({
                        id: 'evidence-control-panel',
                        content: () => (
                            <ControlPanel
                                controlSource="CUSTOMER_REQUEST"
                                data-id="95a12CTy"
                            />
                        ),
                    });
                })();
            }}
        />
    );
});
