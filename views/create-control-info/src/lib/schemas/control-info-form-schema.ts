import { z } from 'zod';
import { sharedControlsController } from '@controllers/controls-owners-candidates';
import type { EditControlRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { toJS } from '@globals/mobx';
import { isDCFCode } from '@helpers/controls';
import {
    type ControlInfoValues,
    sharedControlInfoFormModel,
} from '@models/controls';
import type { FormSchema } from '@ui/forms';

interface SchemaOptions {
    initialValues?: Partial<EditControlRequestDto>;
}

/**
 * Builds either your “new control” form (no args) or
 * your “edit control” (controls details page info card) form (pass in initialValues).
 */
export const buildControlInfoFormSchema = (
    options: SchemaOptions = {},
): FormSchema => {
    const isEdit = Boolean(options.initialValues);
    const vals =
        options.initialValues ?? sharedControlInfoFormModel.storedValues;

    const nameValidator = z
        .string({ message: t`Name is required` })
        .min(1, { message: t`Name is required` })
        .refine(
            async (v) => {
                if (!v.trim()) {
                    return true;
                }
                try {
                    if (isEdit && v === options.initialValues?.name) {
                        return true;
                    }

                    return !(await sharedControlInfoFormModel.checkControlNameExists(
                        v,
                    ));
                } catch {
                    return true;
                }
            },
            {
                message: t`Control name already exists, please choose another control name.`,
            },
        );

    const codeValidator = z
        .string()
        .min(1, { message: t`Code is required` })
        .max(20, { message: t`Control Code cannot exceed 20 characters` })
        .refine(
            (v) => {
                return !v.includes(' ');
            },
            {
                message: t`Control Code cannot contain spaces`,
            },
        )
        .refine(
            (v) => {
                return !v.toUpperCase().startsWith('DCF');
            },
            {
                message: t`DCF is reserved, please choose another prefix`,
            },
        )
        .refine(
            async (v) => {
                if (!v.trim()) {
                    return true;
                }
                try {
                    return !(await sharedControlInfoFormModel.checkControlCodeExists(
                        v,
                    ));
                } catch {
                    return true;
                }
            },
            {
                message: t`Control code already exists, please choose another code.`,
            },
        );

    const schema: FormSchema = {
        name: {
            type: 'text',
            label: t`Name`,
            isOptional: false,
            validator: nameValidator,
            initialValue: vals.name || '',
        },
        code: {
            type: 'text',
            label: t`Code`,
            isOptional: false,
            ...(isEdit
                ? {
                      readOnly: isDCFCode(vals.code),
                  }
                : {
                      validator: codeValidator,
                  }),
            initialValue: vals.code || '',
            helpText: isEdit ? undefined : t`Ex: DRA-XXX`,
        },
        // only show “owner” when creating
        ...(isEdit
            ? {}
            : {
                  owner: {
                      type: 'combobox',
                      label: t`Owner`,
                      isOptional: true,
                      isMultiSelect: true,
                      helpText: t`You can add more than one owner`,
                      hasMore: toJS(sharedControlsController.hasMore),
                      isLoading: toJS(sharedControlsController.isLoading),
                      loaderLabel: t`Loading...`,
                      options: toJS(sharedControlsController.accumulatedData),
                      onFetchOptions:
                          sharedControlsController.loadControlOwnersCandidates,
                      removeAllSelectedItemsLabel: t`Remove all owners`,
                      getRemoveIndividualSelectedItemClickLabel: ({
                          itemLabel,
                      }: {
                          itemLabel: string;
                      }) => t`Remove ${itemLabel}`,
                      getSearchEmptyState: () => t`No results found`,
                      initialValue:
                          toJS((vals as ControlInfoValues).owner) ?? [],
                      placeholder: t`Search by name`,
                  },
              }),
        description: {
            type: 'textarea',
            label: t`Description`,
            isOptional: false,
            validator: z
                .string()
                .min(1, { message: t`Description is required` }),
            initialValue: vals.description || '',
            placeholder: t`Explains the purpose of this control`,
            maxCharacters: 400,
        },
        question: {
            type: 'textarea',
            label: t`Question`,
            isOptional: true,
            initialValue: vals.question || '',
            placeholder: t`Asks what the control needs to accomplish`,
            maxCharacters: 400,
        },
        activity: {
            type: 'textarea',
            label: t`Activities`,
            isOptional: true,
            initialValue: vals.activity || '',
            placeholder: t`Answers the control question and outlines what the control accomplishes`,
            maxCharacters: 400,
        },
    };

    return schema;
};
