import { ViewEditCardComponent } from '@components/view-edit-card';
import {
    sharedControlCustomFieldsController,
    sharedControlDetailsController,
    sharedControlsUpdateMutationController,
} from '@controllers/controls';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { type FormValues, useFormSubmit } from '@ui/forms';
import { ControlsInfoForm } from '../components/control-info/controls-info-form.component';
import { ControlsInfoReadOnlyCard } from '../components/control-info/controls-info-read-only-card.component';
import { ControlLinkedWorkspaceCardComponent } from '../components/control-linked-workspace/control-linked-workspace-card.component';
import { ControlMetricsGridComponent } from '../components/control-metrics/control-metrics-grid-container.component';
import { ControlsOverviewReviewCardComponent } from '../components/control-review-approval/control-review-card.component';

export const ControlsOverviewView = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const { controlCustomFields } = sharedControlCustomFieldsController;
    const { updateControl, isControlUpdateLoading, hasControlUpdateError } =
        sharedControlsUpdateMutationController;
    const { controlDetails } = sharedControlDetailsController;
    const customFieldsData = controlCustomFields[0]?.customFields ?? [];
    const { hasWriteControlPermission } = sharedFeatureAccessModel;

    const handleOnSubmit = (values: FormValues) => {
        if (!controlDetails) {
            return;
        }
        const customFieldsValues =
            sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                values,
                customFieldsData,
            );

        updateControl(
            controlDetails.id,
            {
                name: values.name as string,
                code: values.code as string,
                description: values.description as string,
                question: values.question ? (values.question as string) : null,
                activity: values.activity ? (values.activity as string) : null,
            },
            customFieldsValues,
        );
    };

    return (
        <Stack
            data-testid="ControlsOverviewView"
            data-id="b7swgWwc"
            direction="column"
            gap="4x"
        >
            <ControlMetricsGridComponent />
            <Grid gap="4x" columns="repeat(2, 1fr)" rows="2">
                <Box gridRowStart="1" gridColumn="1">
                    <ViewEditCardComponent
                        title={t`Info`}
                        readOnlyComponent={<ControlsInfoReadOnlyCard />}
                        data-testid="ControlsOverviewInfoCardComponent"
                        data-id="RZzkzsTd"
                        isMutationPending={isControlUpdateLoading}
                        hasMutationError={hasControlUpdateError}
                        editComponent={
                            hasWriteControlPermission ? (
                                <ControlsInfoForm
                                    formRef={formRef}
                                    initialValues={{
                                        name: controlDetails?.name,
                                        code: controlDetails?.code,
                                        description:
                                            controlDetails?.description,
                                        question: controlDetails?.question,
                                        activity: controlDetails?.activity,
                                    }}
                                    onSubmit={handleOnSubmit}
                                />
                            ) : null
                        }
                        onSave={triggerSubmit}
                    />
                </Box>
                <Stack
                    gap="4x"
                    direction="column"
                    align="center"
                    width={'100%'}
                >
                    <Box gridRow="1" gridColumn="2" width={'100%'}>
                        <ControlsOverviewReviewCardComponent />
                    </Box>
                    <Box gridRow="2" gridColumn="2" width={'100%'}>
                        <ControlLinkedWorkspaceCardComponent />
                    </Box>
                </Stack>
            </Grid>
        </Stack>
    );
});
