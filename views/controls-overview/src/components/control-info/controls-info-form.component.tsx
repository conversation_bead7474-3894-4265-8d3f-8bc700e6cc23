import { sharedControlCustomFieldsController } from '@controllers/controls';
import type { EditControlRequestDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { Form, type FormValues } from '@ui/forms';
import { buildControlInfoFormSchema } from '@views/create-control-info';

export interface ControlsInfoFormProps {
    onSubmit: (values: FormValues) => void;
    initialValues?: Partial<EditControlRequestDto>;
    submitButtonLabel?: string;
    formRef: React.RefObject<HTMLFormElement>;
}

export const ControlsInfoForm = observer(
    ({
        onSubmit,
        formRef,
        initialValues = {},
    }: ControlsInfoFormProps): React.JSX.Element => {
        const { controlCustomFields } = sharedControlCustomFieldsController;
        const customFieldsData = controlCustomFields[0]?.customFields ?? [];

        const baseSchema = buildControlInfoFormSchema({
            initialValues,
        });
        const customFieldsSchema =
            sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
                customFieldsData,
            );
        const schema = {
            ...baseSchema,
            ...customFieldsSchema,
        };

        return (
            <Form
                hasExternalSubmitButton
                formId="controls-info-form"
                ref={formRef}
                schema={schema}
                data-id="controls-info-form"
                onSubmit={onSubmit}
            />
        );
    },
);
